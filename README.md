# PDF Document Analyzer

A Python application with a graphical user interface for analyzing and comparing PDF documents.

## Features

- **Single Document Analysis**: Extract text, metadata, and basic statistics from a single PDF
- **Document Comparison**: Compare two PDF documents and calculate similarity metrics
- **Batch Processing**: Process multiple PDF documents in a directory at once

## Installation

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

## Usage

1. Run the application:
```bash
python document_analyser.py
```

2. Select your operation mode:
   - **Analyze Single Document**: Analyze one PDF file
   - **Compare Two Documents**: Compare two PDF files for similarities and differences
   - **Batch Process Documents**: Process all PDF files in a directory

3. Set the output directory where reports will be saved

4. Select your document(s):
   - For single analysis: Choose one PDF file
   - For comparison: Choose two PDF files
   - For batch processing: Choose a directory containing PDF files

5. Click "Start Analysis" to begin processing

## Output

The application generates:

### Single Document Analysis
- JSON report with metadata and statistics
- Extracted text file

### Document Comparison
- JSON report comparing two documents with similarity metrics

### Batch Processing
- Individual analysis reports for each PDF
- Summary report of the batch processing

## Requirements

- Python 3.6+
- PyPDF2
- PyMuPDF (fitz)
- tkinter (usually included with Python)

## Features of the Analysis

- **Text Extraction**: Extracts all text content from PDF documents
- **Metadata Analysis**: Retrieves document properties like title, author, creation date
- **Statistics**: Word count, character count, line count, page count
- **Similarity Comparison**: Uses sequence matching to compare document similarity
- **Error Handling**: Graceful handling of corrupted or problematic PDF files
