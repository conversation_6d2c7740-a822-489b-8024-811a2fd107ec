import tkinter as tk
from tkinter import filedialog
from tkinter import messagebox

class DocumentAnalyzerApp:
    def __init__(self, master):
        self.master = master
        master.title("Document Analyzer")
        
        # Operation Mode
        self.mode_var = tk.StringVar(value="single")
        tk.Label(master, text="Select Operation Mode").pack(anchor=tk.W)
        
        tk.Radiobutton(master, text="Analyze Single Document",
                       variable=self.mode_var, value="single").pack(anchor=tk.W)
        tk.Radiobutton(master, text="Compare Two Documents",
                       variable=self.mode_var, value="compare").pack(anchor=tk.W)
        tk.Radiobutton(master, text="Batch Process Documents",
                       variable=self.mode_var, value="batch").pack(anchor=tk.W)

        # Output Settings
        tk.Label(master, text="Output Settings").pack(anchor=tk.W)
        tk.Label(master, text="Output Directory:").pack(anchor=tk.W)
        
        self.output_dir = tk.StringVar(value="reports")
        self.directory_entry = tk.Entry(master, textvariable=self.output_dir, width=50)
        self.directory_entry.pack()
        
        tk.Button(master, text="Browse", command=self.browse_directory).pack()
        
        # Document Selection
        tk.Label(master, text="Select Document").pack(anchor=tk.W)
        self.document_path = tk.StringVar()
        self.document_entry = tk.Entry(master, textvariable=self.document_path, width=50)
        self.document_entry.pack()
        
        tk.Button(master, text="Browse", command=self.browse_document).pack()

        # Start Analysis Button
        tk.Button(master, text="Start Analysis", command=self.start_analysis).pack()

    def browse_directory(self):
        directory = filedialog.askdirectory()
        if directory:
            self.output_dir.set(directory)
    
    def browse_document(self):
        file_path = filedialog.askopenfilename()
        if file_path:
            self.document_path.set(file_path)
    
    def start_analysis(self):
        mode = self.mode_var.get()
        output_dir = self.output_dir.get()
        document = self.document_path.get()
        
        if not output_dir or not document:
            messagebox.showerror("Error", "Please specify the output directory and document.")
            return
        
        # Placeholder for actual document analysis logic
        messagebox.showinfo("Analysis Started", f"Starting analysis in {mode} mode...\nOutput Directory: {output_dir}\nDocument: {document}")
        # Add your document analysis logic here

if __name__ == "__main__":
    root = tk.Tk()
    app = DocumentAnalyzerApp(root)
    root.mainloop()
