import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import os
import json
from datetime import datetime
try:
    import PyPDF2
    import fitz  # PyMuPDF
    from difflib import SequenceMatcher
except ImportError as e:
    print(f"Missing required libraries: {e}")
    print("Please install: pip install PyPDF2 PyMuPDF")

class DocumentAnalyzerApp:
    def __init__(self, master):
        self.master = master
        master.title("Document Analyzer")
        master.geometry("600x500")

        # Operation Mode
        self.mode_var = tk.StringVar(value="single")
        mode_frame = tk.Frame(master)
        mode_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(mode_frame, text="Select Operation Mode", font=("Arial", 12, "bold")).pack(anchor=tk.W)

        tk.Radiobutton(mode_frame, text="Analyze Single Document",
                       variable=self.mode_var, value="single",
                       command=self.update_ui_mode).pack(anchor=tk.W)
        tk.Radiobutton(mode_frame, text="Compare Two Documents",
                       variable=self.mode_var, value="compare",
                       command=self.update_ui_mode).pack(anchor=tk.W)
        tk.Radiobutton(mode_frame, text="Batch Process Documents",
                       variable=self.mode_var, value="batch",
                       command=self.update_ui_mode).pack(anchor=tk.W)

        # Output Settings
        output_frame = tk.Frame(master)
        output_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(output_frame, text="Output Settings", font=("Arial", 12, "bold")).pack(anchor=tk.W)
        tk.Label(output_frame, text="Output Directory:").pack(anchor=tk.W)

        dir_frame = tk.Frame(output_frame)
        dir_frame.pack(fill=tk.X)

        self.output_dir = tk.StringVar(value="reports")
        self.directory_entry = tk.Entry(dir_frame, textvariable=self.output_dir, width=50)
        self.directory_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        tk.Button(dir_frame, text="Browse", command=self.browse_directory).pack(side=tk.RIGHT, padx=(5,0))

        # Document Selection Frame
        self.doc_frame = tk.Frame(master)
        self.doc_frame.pack(fill=tk.X, padx=10, pady=5)

        self.setup_document_selection()

        # Progress Bar
        self.progress = ttk.Progressbar(master, mode='indeterminate')
        self.progress.pack(fill=tk.X, padx=10, pady=5)

        # Start Analysis Button
        tk.Button(master, text="Start Analysis", command=self.start_analysis,
                 bg="#4CAF50", fg="white", font=("Arial", 12, "bold")).pack(pady=10)

    def setup_document_selection(self):
        """Setup document selection UI based on current mode"""
        # Clear existing widgets
        for widget in self.doc_frame.winfo_children():
            widget.destroy()

        tk.Label(self.doc_frame, text="Document Selection", font=("Arial", 12, "bold")).pack(anchor=tk.W)

        mode = self.mode_var.get()

        if mode == "single":
            self.setup_single_document_selection()
        elif mode == "compare":
            self.setup_compare_documents_selection()
        elif mode == "batch":
            self.setup_batch_documents_selection()

    def setup_single_document_selection(self):
        """Setup UI for single document selection"""
        tk.Label(self.doc_frame, text="Select Document:").pack(anchor=tk.W)

        doc_frame = tk.Frame(self.doc_frame)
        doc_frame.pack(fill=tk.X)

        self.document_path = tk.StringVar()
        self.document_entry = tk.Entry(doc_frame, textvariable=self.document_path, width=50)
        self.document_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        tk.Button(doc_frame, text="Browse",
                 command=lambda: self.browse_document(self.document_path)).pack(side=tk.RIGHT, padx=(5,0))

    def setup_compare_documents_selection(self):
        """Setup UI for comparing two documents"""
        tk.Label(self.doc_frame, text="Select First Document:").pack(anchor=tk.W)

        doc1_frame = tk.Frame(self.doc_frame)
        doc1_frame.pack(fill=tk.X, pady=(0,5))

        self.document1_path = tk.StringVar()
        self.document1_entry = tk.Entry(doc1_frame, textvariable=self.document1_path, width=50)
        self.document1_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        tk.Button(doc1_frame, text="Browse",
                 command=lambda: self.browse_document(self.document1_path)).pack(side=tk.RIGHT, padx=(5,0))

        tk.Label(self.doc_frame, text="Select Second Document:").pack(anchor=tk.W)

        doc2_frame = tk.Frame(self.doc_frame)
        doc2_frame.pack(fill=tk.X)

        self.document2_path = tk.StringVar()
        self.document2_entry = tk.Entry(doc2_frame, textvariable=self.document2_path, width=50)
        self.document2_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        tk.Button(doc2_frame, text="Browse",
                 command=lambda: self.browse_document(self.document2_path)).pack(side=tk.RIGHT, padx=(5,0))

    def setup_batch_documents_selection(self):
        """Setup UI for batch document processing"""
        tk.Label(self.doc_frame, text="Select Directory Containing Documents:").pack(anchor=tk.W)

        batch_frame = tk.Frame(self.doc_frame)
        batch_frame.pack(fill=tk.X)

        self.batch_dir_path = tk.StringVar()
        self.batch_entry = tk.Entry(batch_frame, textvariable=self.batch_dir_path, width=50)
        self.batch_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        tk.Button(batch_frame, text="Browse", command=self.browse_batch_directory).pack(side=tk.RIGHT, padx=(5,0))

    def update_ui_mode(self):
        """Update UI when mode changes"""
        self.setup_document_selection()

    def browse_directory(self):
        directory = filedialog.askdirectory()
        if directory:
            self.output_dir.set(directory)

    def browse_document(self, path_var):
        file_path = filedialog.askopenfilename(
            title="Select PDF Document",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
        )
        if file_path:
            path_var.set(file_path)

    def browse_batch_directory(self):
        directory = filedialog.askdirectory(title="Select Directory with PDF Documents")
        if directory:
            self.batch_dir_path.set(directory)

    def extract_text_from_pdf(self, pdf_path):
        """Extract text from PDF using PyMuPDF"""
        try:
            doc = fitz.open(pdf_path)
            text = ""
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text += page.get_text()
            doc.close()
            return text
        except Exception as e:
            print(f"Error extracting text from {pdf_path}: {e}")
            return ""

    def get_pdf_metadata(self, pdf_path):
        """Extract metadata from PDF"""
        try:
            doc = fitz.open(pdf_path)
            metadata = doc.metadata
            page_count = len(doc)
            doc.close()

            return {
                "title": metadata.get("title", ""),
                "author": metadata.get("author", ""),
                "subject": metadata.get("subject", ""),
                "creator": metadata.get("creator", ""),
                "producer": metadata.get("producer", ""),
                "creation_date": metadata.get("creationDate", ""),
                "modification_date": metadata.get("modDate", ""),
                "page_count": page_count
            }
        except Exception as e:
            print(f"Error extracting metadata from {pdf_path}: {e}")
            return {}

    def analyze_single_document(self, pdf_path, output_dir):
        """Analyze a single PDF document"""
        try:
            # Create output directory if it doesn't exist
            os.makedirs(output_dir, exist_ok=True)

            # Extract text and metadata
            text = self.extract_text_from_pdf(pdf_path)
            metadata = self.get_pdf_metadata(pdf_path)

            # Basic analysis
            word_count = len(text.split())
            char_count = len(text)
            line_count = len(text.split('\n'))

            # Create analysis report
            report = {
                "document_path": pdf_path,
                "analysis_date": datetime.now().isoformat(),
                "metadata": metadata,
                "statistics": {
                    "word_count": word_count,
                    "character_count": char_count,
                    "line_count": line_count
                },
                "text_preview": text[:500] + "..." if len(text) > 500 else text
            }

            # Save report
            filename = os.path.splitext(os.path.basename(pdf_path))[0]
            report_path = os.path.join(output_dir, f"{filename}_analysis.json")

            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)

            # Save extracted text
            text_path = os.path.join(output_dir, f"{filename}_text.txt")
            with open(text_path, 'w', encoding='utf-8') as f:
                f.write(text)

            return report_path, text_path

        except Exception as e:
            raise Exception(f"Error analyzing document: {e}")

    def compare_documents(self, pdf1_path, pdf2_path, output_dir):
        """Compare two PDF documents"""
        try:
            # Create output directory if it doesn't exist
            os.makedirs(output_dir, exist_ok=True)

            # Extract text from both documents
            text1 = self.extract_text_from_pdf(pdf1_path)
            text2 = self.extract_text_from_pdf(pdf2_path)

            # Get metadata
            metadata1 = self.get_pdf_metadata(pdf1_path)
            metadata2 = self.get_pdf_metadata(pdf2_path)

            # Calculate similarity
            similarity = SequenceMatcher(None, text1, text2).ratio()

            # Basic statistics
            stats1 = {
                "word_count": len(text1.split()),
                "character_count": len(text1),
                "line_count": len(text1.split('\n'))
            }

            stats2 = {
                "word_count": len(text2.split()),
                "character_count": len(text2),
                "line_count": len(text2.split('\n'))
            }

            # Create comparison report
            comparison_report = {
                "comparison_date": datetime.now().isoformat(),
                "document1": {
                    "path": pdf1_path,
                    "metadata": metadata1,
                    "statistics": stats1
                },
                "document2": {
                    "path": pdf2_path,
                    "metadata": metadata2,
                    "statistics": stats2
                },
                "comparison": {
                    "text_similarity": similarity,
                    "word_count_difference": abs(stats1["word_count"] - stats2["word_count"]),
                    "character_count_difference": abs(stats1["character_count"] - stats2["character_count"]),
                    "page_count_difference": abs(metadata1.get("page_count", 0) - metadata2.get("page_count", 0))
                }
            }

            # Save comparison report
            filename1 = os.path.splitext(os.path.basename(pdf1_path))[0]
            filename2 = os.path.splitext(os.path.basename(pdf2_path))[0]
            report_path = os.path.join(output_dir, f"comparison_{filename1}_vs_{filename2}.json")

            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(comparison_report, f, indent=2, ensure_ascii=False)

            return report_path

        except Exception as e:
            raise Exception(f"Error comparing documents: {e}")

    def batch_process_documents(self, batch_dir, output_dir):
        """Process all PDF documents in a directory"""
        try:
            # Create output directory if it doesn't exist
            os.makedirs(output_dir, exist_ok=True)

            # Find all PDF files
            pdf_files = [f for f in os.listdir(batch_dir) if f.lower().endswith('.pdf')]

            if not pdf_files:
                raise Exception("No PDF files found in the selected directory")

            results = []
            for pdf_file in pdf_files:
                pdf_path = os.path.join(batch_dir, pdf_file)
                try:
                    report_path, text_path = self.analyze_single_document(pdf_path, output_dir)
                    results.append({
                        "file": pdf_file,
                        "status": "success",
                        "report_path": report_path,
                        "text_path": text_path
                    })
                except Exception as e:
                    results.append({
                        "file": pdf_file,
                        "status": "error",
                        "error": str(e)
                    })

            # Save batch processing summary
            summary_path = os.path.join(output_dir, "batch_processing_summary.json")
            summary = {
                "processing_date": datetime.now().isoformat(),
                "source_directory": batch_dir,
                "total_files": len(pdf_files),
                "successful": len([r for r in results if r["status"] == "success"]),
                "failed": len([r for r in results if r["status"] == "error"]),
                "results": results
            }

            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(summary, f, indent=2, ensure_ascii=False)

            return summary_path

        except Exception as e:
            raise Exception(f"Error in batch processing: {e}")

    def start_analysis(self):
        """Start the document analysis based on selected mode"""
        mode = self.mode_var.get()
        output_dir = self.output_dir.get()

        if not output_dir:
            messagebox.showerror("Error", "Please specify the output directory.")
            return

        try:
            self.progress.start()

            if mode == "single":
                if not hasattr(self, 'document_path') or not self.document_path.get():
                    messagebox.showerror("Error", "Please select a document to analyze.")
                    return

                report_path, text_path = self.analyze_single_document(
                    self.document_path.get(), output_dir
                )
                messagebox.showinfo("Analysis Complete",
                                  f"Analysis completed successfully!\n\nReport saved to: {report_path}\nText saved to: {text_path}")

            elif mode == "compare":
                if (not hasattr(self, 'document1_path') or not self.document1_path.get() or
                    not hasattr(self, 'document2_path') or not self.document2_path.get()):
                    messagebox.showerror("Error", "Please select both documents to compare.")
                    return

                report_path = self.compare_documents(
                    self.document1_path.get(),
                    self.document2_path.get(),
                    output_dir
                )
                messagebox.showinfo("Comparison Complete",
                                  f"Document comparison completed successfully!\n\nReport saved to: {report_path}")

            elif mode == "batch":
                if not hasattr(self, 'batch_dir_path') or not self.batch_dir_path.get():
                    messagebox.showerror("Error", "Please select a directory containing PDF documents.")
                    return

                summary_path = self.batch_process_documents(
                    self.batch_dir_path.get(), output_dir
                )
                messagebox.showinfo("Batch Processing Complete",
                                  f"Batch processing completed successfully!\n\nSummary saved to: {summary_path}")

        except Exception as e:
            messagebox.showerror("Error", f"An error occurred during analysis:\n{str(e)}")

        finally:
            self.progress.stop()

if __name__ == "__main__":
    root = tk.Tk()
    app = DocumentAnalyzerApp(root)
    root.mainloop()
